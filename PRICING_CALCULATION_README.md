# Pricing Calculation from MinIO JSON Files

This project provides scripts to calculate total pricing from JSON files stored in a MinIO bucket, where the object names are retrieved from MongoDB.

## Prerequisites

1. **Environment Variables**: Create a `.env` file with your MongoDB URI:
   ```
   MONGO_URI=mongodb://************:8300/?directConnection=true&serverSelectionTimeoutMS=2000&appName=mongosh+2.2.10
   ```

2. **Dependencies**: Install required packages:
   ```bash
   uv add minio python-dotenv pymongo
   ```

## Configuration

The MinIO configuration is hardcoded in the script based on your provided credentials:
- **Access Key**: minio-admin
- **Secret Key**: aLongPassword123
- **Bucket Name**: aroma.agsociar
- **MinIO URL**: minio.nextai.asia
- **Secure**: false

## JSON File Structure

The script expects JSON files with the following structure:
```json
{
  "transcription": [...],
  "analysis": {...},
  "pricing": {
    "transcription_price_nep": 0.051179775000000004,
    "analysis_price_nep": 0.028615874999999995,
    "total_price_nep": 0.07979565
  },
  "usage_metadata": {...}
}
```

The script specifically extracts the `pricing.total_price_nep` value from each JSON file.

## Usage

### Interactive Mode

Run the main script interactively:
```bash
python calculate_total_pricing.py
```

You'll be prompted to enter:
- MongoDB database name
- MongoDB collection name
- Optional query filter (as JSON)

### Programmatic Usage

Use the `calculate_pricing_from_mongo` function in your own code:

```python
from calculate_total_pricing import calculate_pricing_from_mongo

# Calculate total for all documents
total = calculate_pricing_from_mongo("your_db", "your_collection")

# Calculate with a filter
query = {"status": "processed"}
total_filtered = calculate_pricing_from_mongo("your_db", "your_collection", query)
```

### Example Usage

See `example_usage.py` for more detailed examples including:
- Calculating pricing for all documents
- Using filter queries
- Date range filtering

## MongoDB Schema Requirements

The script expects MongoDB documents to contain one of these fields for the object name:
- `object_name`
- `filename`
- `file_path`

If your MongoDB schema uses a different field name, you'll need to modify the `get_object_names_from_mongo` function.

## Error Handling

The script includes error handling for:
- MongoDB connection issues
- MinIO connection and file retrieval errors
- JSON parsing errors
- Missing pricing data in JSON files

## Output

The script will:
1. Show progress as it fetches files from MinIO
2. Display individual pricing values as they're processed
3. Show the final total pricing in NPR (Nepalese Rupees)

Example output:
```
Connecting to MongoDB...
Connecting to MinIO...
Fetching object names from MongoDB...
Found 150 object names
Fetching JSON files from MinIO...
Fetching 1/150: file1.json
Added price: 0.079796 NPR
Fetching 2/150: file2.json
Added price: 0.085432 NPR
...
Processed 148 files successfully

==================================================
TOTAL PRICING: 12.345678 NPR
==================================================
```

## Troubleshooting

1. **MongoDB Connection Issues**: Verify your `MONGO_URI` in the `.env` file
2. **MinIO Connection Issues**: Check the MinIO configuration and network connectivity
3. **Missing Object Names**: Ensure your MongoDB documents have the expected field names
4. **JSON Parsing Errors**: Verify that the files in MinIO are valid JSON
5. **Missing Pricing Data**: Check that JSON files contain the `pricing.total_price_nep` field
