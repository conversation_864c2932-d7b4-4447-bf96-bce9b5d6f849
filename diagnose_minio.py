#!/usr/bin/env python3
"""
Diagnostic script to troubleshoot MinIO access issues.
"""

from minio import Minio
import json

# MinIO configurations to test
MINIO_CONFIGS = [
    {
        "name": "Standard port with domain",
        "access_key": "minio-admin",
        "secret_key": "aLongPassword123",
        "bucket_name": "aroma.agsociar",
        "minio_url": "minio.nextai.asia:9000",
        "secure": False
    },
    {
        "name": "No port with domain",
        "access_key": "minio-admin",
        "secret_key": "aLongPassword123",
        "bucket_name": "aroma.agsociar",
        "minio_url": "minio.nextai.asia",
        "secure": False
    },
    {
        "name": "Internal IP with standard port",
        "access_key": "minio-admin",
        "secret_key": "aLongPassword123",
        "bucket_name": "aroma.agsociar",
        "minio_url": "************:9000",
        "secure": False
    },
    {
        "name": "HTTPS with domain",
        "access_key": "minio-admin",
        "secret_key": "aLongPassword123",
        "bucket_name": "aroma.agsociar",
        "minio_url": "minio.nextai.asia",
        "secure": True
    }
]

def test_minio_config(config):
    """Test a specific MinIO configuration."""
    print(f"\n🧪 Testing: {config['name']}")
    print(f"   URL: {config['minio_url']}")
    print(f"   Secure: {config['secure']}")
    print(f"   Bucket: {config['bucket_name']}")
    
    try:
        # Create client
        client = Minio(
            config["minio_url"],
            access_key=config["access_key"],
            secret_key=config["secret_key"],
            secure=config["secure"]
        )
        
        # Test 1: List buckets
        print("   🔄 Testing bucket listing...")
        buckets = list(client.list_buckets())
        bucket_names = [b.name for b in buckets]
        print(f"   ✅ Found {len(buckets)} buckets: {bucket_names}")
        
        # Test 2: Check target bucket
        if config["bucket_name"] in bucket_names:
            print(f"   ✅ Target bucket '{config['bucket_name']}' found")
            
            # Test 3: List objects in bucket
            print("   🔄 Testing object listing...")
            objects = list(client.list_objects(config["bucket_name"], recursive=True))
            print(f"   ✅ Found {len(objects)} objects in bucket")
            
            if objects:
                # Test 4: Try to access first object
                first_object = objects[0]
                print(f"   🔄 Testing object access: {first_object.object_name}")
                
                try:
                    # Check object stats
                    stat = client.stat_object(config["bucket_name"], first_object.object_name)
                    print(f"   ✅ Object accessible - Size: {stat.size} bytes")
                    
                    # Try to read a small portion
                    response = client.get_object(config["bucket_name"], first_object.object_name)
                    content = response.read(100)  # Read first 100 bytes
                    response.close()
                    response.release_conn()
                    print(f"   ✅ Object readable - First 100 bytes retrieved")
                    
                    return True, config
                    
                except Exception as obj_error:
                    print(f"   ❌ Object access failed: {obj_error}")
                    return False, None
            else:
                print(f"   ⚠️  No objects found in bucket")
                return True, config  # Connection works but bucket is empty
                
        else:
            print(f"   ❌ Target bucket '{config['bucket_name']}' not found")
            print(f"   💡 Available buckets: {bucket_names}")
            return False, None
            
    except Exception as e:
        print(f"   ❌ Connection failed: {e}")
        return False, None

def suggest_solutions():
    """Suggest potential solutions for common MinIO access issues."""
    print("\n" + "="*60)
    print("🔧 TROUBLESHOOTING SUGGESTIONS")
    print("="*60)
    
    suggestions = [
        "1. **Check Network Access**: Ensure you can reach the MinIO server from your network",
        "2. **Verify Credentials**: Double-check the access_key and secret_key",
        "3. **Check Bucket Permissions**: Ensure your credentials have read access to the bucket",
        "4. **Try Internal IP**: If external domain fails, try the internal IP address",
        "5. **Check Firewall**: Ensure port 9000 (or the MinIO port) is not blocked",
        "6. **Verify Bucket Name**: Ensure the bucket name 'aroma.agsociar' is correct",
        "7. **Check MinIO Server Status**: Verify the MinIO server is running and accessible"
    ]
    
    for suggestion in suggestions:
        print(f"   {suggestion}")
    
    print(f"\n💡 **Alternative Approaches:**")
    print(f"   - Use MinIO web console to verify access: http://minio.nextai.asia:9001")
    print(f"   - Use MinIO CLI (mc) to test: mc ls minio-alias/aroma.agsociar")
    print(f"   - Check with system administrator for correct credentials/endpoints")

def main():
    """Main diagnostic function."""
    print("🔍 MinIO Connection Diagnostics")
    print("="*60)
    
    working_configs = []
    
    for config in MINIO_CONFIGS:
        success, working_config = test_minio_config(config)
        if success:
            working_configs.append(working_config)
    
    print(f"\n" + "="*60)
    print("📊 DIAGNOSTIC RESULTS")
    print("="*60)
    
    if working_configs:
        print(f"✅ Found {len(working_configs)} working configuration(s):")
        for i, config in enumerate(working_configs, 1):
            print(f"\n{i}. {config['name']}")
            print(f"   URL: {config['minio_url']}")
            print(f"   Secure: {config['secure']}")
            
        print(f"\n💡 **Recommended Action:**")
        print(f"   Update your calculate_total_pricing.py script to use the working configuration")
        
        # Show code snippet
        best_config = working_configs[0]
        print(f"\n📝 **Code to use:**")
        print(f"```python")
        print(f"MINIO_CONFIG = {{")
        print(f"    'access_key': '{best_config['access_key']}',")
        print(f"    'secret_key': '{best_config['secret_key']}',")
        print(f"    'bucket_name': '{best_config['bucket_name']}',")
        print(f"    'minio_url': '{best_config['minio_url']}',")
        print(f"    'secure': {best_config['secure']}")
        print(f"}}")
        print(f"```")
        
    else:
        print("❌ No working configurations found")
        suggest_solutions()

if __name__ == "__main__":
    main()
