#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to explore MongoDB databases and collections to help find the right data.
"""

import os
from dotenv import load_dotenv
from pymongo import MongoClient
import json

# Load environment variables
load_dotenv()

def connect_to_mongo() -> MongoClient:
    """Connect to MongoDB using the URI from environment variables."""
    mongo_uri = os.getenv('MONGO_URI')
    if not mongo_uri:
        raise ValueError("MONGO_URI not found in environment variables")
    
    client = MongoClient(mongo_uri)
    return client

def explore_databases(client: MongoClient):
    """List all databases and their collections."""
    print("📁 Available Databases:")
    print("-" * 50)
    
    databases = client.list_database_names()
    for db_name in databases:
        print(f"\n🗂️  Database: {db_name}")
        
        try:
            db = client[db_name]
            collections = db.list_collection_names()
            
            if collections:
                print(f"   Collections ({len(collections)}):")
                for collection_name in collections[:10]:  # Show first 10 collections
                    collection = db[collection_name]
                    count = collection.estimated_document_count()
                    print(f"     📄 {collection_name} ({count} documents)")
                
                if len(collections) > 10:
                    print(f"     ... and {len(collections) - 10} more collections")
            else:
                print("     No collections found")
                
        except Exception as e:
            print(f"     ❌ Error accessing database: {e}")

def explore_collection(client: MongoClient, database_name: str, collection_name: str):
    """Explore a specific collection to understand its structure."""
    print(f"\n🔍 Exploring Collection: {database_name}.{collection_name}")
    print("-" * 50)
    
    try:
        db = client[database_name]
        collection = db[collection_name]
        
        # Get collection stats
        count = collection.estimated_document_count()
        print(f"📊 Total documents: {count}")
        
        if count > 0:
            # Get a sample document
            sample_doc = collection.find_one()
            if sample_doc:
                print(f"\n📄 Sample document structure:")
                # Remove _id for cleaner display and limit depth
                if '_id' in sample_doc:
                    del sample_doc['_id']
                
                print(json.dumps(sample_doc, indent=2, default=str)[:1000] + "..." if len(str(sample_doc)) > 1000 else json.dumps(sample_doc, indent=2, default=str))
                
                # Check for fields that might contain object names
                potential_object_fields = []
                for key in sample_doc.keys():
                    if any(keyword in key.lower() for keyword in ['object', 'file', 'name', 'path', 'url']):
                        potential_object_fields.append(key)
                
                if potential_object_fields:
                    print(f"\n🎯 Potential object name fields: {potential_object_fields}")
                
                # Check if this looks like it contains pricing data
                if 'pricing' in sample_doc:
                    print(f"\n💰 Found pricing data structure:")
                    print(json.dumps(sample_doc['pricing'], indent=2, default=str))
        
    except Exception as e:
        print(f"❌ Error exploring collection: {e}")

def search_for_pricing_collections(client: MongoClient):
    """Search for collections that might contain pricing data."""
    print("\n🔍 Searching for collections with pricing data...")
    print("-" * 50)
    
    databases = client.list_database_names()
    found_collections = []
    
    for db_name in databases:
        try:
            db = client[db_name]
            collections = db.list_collection_names()
            
            for collection_name in collections:
                collection = db[collection_name]
                
                # Check if any document has pricing data
                sample_with_pricing = collection.find_one({"pricing": {"$exists": True}})
                if sample_with_pricing:
                    count_with_pricing = collection.count_documents({"pricing": {"$exists": True}})
                    total_count = collection.estimated_document_count()
                    
                    found_collections.append({
                        "database": db_name,
                        "collection": collection_name,
                        "total_docs": total_count,
                        "docs_with_pricing": count_with_pricing,
                        "sample_pricing": sample_with_pricing.get('pricing', {})
                    })
                    
                    print(f"✅ {db_name}.{collection_name}")
                    print(f"   📊 {count_with_pricing}/{total_count} documents have pricing data")
                    
                    if 'total_price_nep' in sample_with_pricing.get('pricing', {}):
                        price = sample_with_pricing['pricing']['total_price_nep']
                        print(f"   💰 Sample total_price_nep: {price}")
                    
                    print()
                    
        except Exception as e:
            # Skip databases we can't access
            continue
    
    return found_collections

def main():
    """Main function to explore MongoDB."""
    try:
        print("🔗 Connecting to MongoDB...")
        client = connect_to_mongo()
        
        print("✅ Connected successfully!\n")
        
        while True:
            print("\n" + "=" * 60)
            print("🧭 MongoDB Explorer")
            print("=" * 60)
            print("1. List all databases and collections")
            print("2. Explore a specific collection")
            print("3. Search for collections with pricing data")
            print("4. Exit")
            
            choice = input("\nEnter your choice (1-4): ").strip()
            
            if choice == "1":
                explore_databases(client)
                
            elif choice == "2":
                db_name = input("Enter database name: ").strip()
                collection_name = input("Enter collection name: ").strip()
                explore_collection(client, db_name, collection_name)
                
            elif choice == "3":
                found = search_for_pricing_collections(client)
                if found:
                    print(f"\n🎯 Found {len(found)} collections with pricing data")
                    print("You can use these in your pricing calculation script!")
                else:
                    print("\n❌ No collections with pricing data found")
                    
            elif choice == "4":
                print("👋 Goodbye!")
                break
                
            else:
                print("❌ Invalid choice. Please enter 1-4.")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    main()
