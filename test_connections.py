#!/usr/bin/env python3
"""
Test script to verify MongoDB and MinIO connections.
"""

import os
from dotenv import load_dotenv
from pymongo import MongoClient
from minio import Minio

# Load environment variables
load_dotenv()

# MinIO configuration - try different ports and configurations
MINIO_CONFIGS = [
    {
        "access_key": "minio-admin",
        "secret_key": "aLongPassword123",
        "bucket_name": "aroma.agsociar",
        "minio_url": "minio.nextai.asia:9000",
        "secure": False
    },
    {
        "access_key": "minio-admin",
        "secret_key": "aLongPassword123",
        "bucket_name": "aroma.agsociar",
        "minio_url": "minio.nextai.asia",
        "secure": False
    },
    {
        "access_key": "minio-admin",
        "secret_key": "aLongPassword123",
        "bucket_name": "aroma.agsociar",
        "minio_url": "minio.nextai.asia:443",
        "secure": True
    }
]

def test_mongo_connection():
    """Test MongoDB connection."""
    try:
        mongo_uri = os.getenv('MONGO_URI')
        if not mongo_uri:
            print("❌ MONGO_URI not found in environment variables")
            return False
        
        print(f"🔄 Testing MongoDB connection to: {mongo_uri}")
        client = MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
        
        # Test the connection
        client.admin.command('ping')
        print("✅ MongoDB connection successful")
        
        # List databases
        databases = client.list_database_names()
        print(f"📁 Available databases: {databases}")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ MongoDB connection failed: {e}")
        return False

def test_minio_connection():
    """Test MinIO connection with different configurations."""
    for i, config in enumerate(MINIO_CONFIGS):
        try:
            print(f"🔄 Testing MinIO connection #{i+1} to: {config['minio_url']} (secure: {config['secure']})")
            client = Minio(
                config["minio_url"],
                access_key=config["access_key"],
                secret_key=config["secret_key"],
                secure=config["secure"]
            )

            # Test the connection by listing buckets
            buckets = client.list_buckets()
            print("✅ MinIO connection successful")

            # List available buckets
            bucket_names = [bucket.name for bucket in buckets]
            print(f"🪣 Available buckets: {bucket_names}")

            # Check if our target bucket exists
            if config["bucket_name"] in bucket_names:
                print(f"✅ Target bucket '{config['bucket_name']}' found")

                # List some objects in the bucket (first 10)
                objects = client.list_objects(config["bucket_name"], recursive=True)
                object_list = []
                for j, obj in enumerate(objects):
                    if j >= 10:  # Limit to first 10 objects
                        break
                    object_list.append(obj.object_name)

                if object_list:
                    print(f"📄 Sample objects in bucket (first 10): {object_list}")
                else:
                    print("📄 No objects found in bucket")
            else:
                print(f"❌ Target bucket '{config['bucket_name']}' not found")
                continue

            # If we get here, this configuration worked
            print(f"✅ Working MinIO configuration found: {config}")
            return True, config

        except Exception as e:
            print(f"❌ MinIO connection #{i+1} failed: {e}")
            continue

    print("❌ All MinIO configurations failed")
    return False, None

def main():
    """Test both connections."""
    print("🧪 Testing connections...\n")

    mongo_ok = test_mongo_connection()
    print()
    minio_ok, working_config = test_minio_connection()

    print(f"\n{'='*50}")
    if mongo_ok and minio_ok:
        print("🎉 All connections successful! You can proceed with the pricing calculation.")
        if working_config:
            print(f"💡 Use this MinIO configuration in your scripts:")
            print(f"   URL: {working_config['minio_url']}")
            print(f"   Secure: {working_config['secure']}")
    else:
        print("⚠️  Some connections failed. Please check your configuration.")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
