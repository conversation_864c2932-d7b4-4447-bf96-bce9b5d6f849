import csv
from datetime import datetime, timedelta
import random
import uuid
from typing import List, Dict, Any

def generate_sample_chat_data(num_records: int = 100) -> List[Dict[str, Any]]:
    """
    Generates sample chat data for testing purposes.

    Args:
        num_records: Number of chat records to generate

    Returns:
        List of sample chat data dictionaries
    """
    sample_data = []

    # Sample user names
    user_names = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
                  "<PERSON>", "<PERSON>", "<PERSON>", "Ivy Chen", "<PERSON>"]

    # Sample chat messages
    user_messages = [
        "Hello, I'm interested in your product pricing",
        "Can you tell me about your enterprise solutions?",
        "What's the difference between your basic and premium plans?",
        "I need help with implementation",
        "Do you offer customer support?",
        "What are your payment options?",
        "Can I get a demo of your software?",
        "How does your free trial work?",
        "I'm looking for a solution for my team of 50 people",
        "What integrations do you support?"
    ]

    assistant_responses = [
        "Thank you for your interest! I'd be happy to help you with pricing information.",
        "Our enterprise solutions are designed for large organizations with advanced needs.",
        "Let me explain the key differences between our plans.",
        "I can connect you with our implementation team for detailed guidance.",
        "Yes, we provide 24/7 customer support for all our plans.",
        "We accept all major credit cards and offer annual billing discounts.",
        "Absolutely! I can schedule a personalized demo for you.",
        "Our free trial gives you full access for 14 days with no commitment.",
        "For teams of that size, I'd recommend our Business plan.",
        "We integrate with over 100 popular business tools."
    ]

    base_time = datetime.now() - timedelta(days=30)

    for i in range(num_records):
        conversation_id = str(uuid.uuid4())
        user_name = random.choice(user_names)
        timestamp = (base_time + timedelta(
            days=random.randint(0, 30),
            hours=random.randint(0, 23),
            minutes=random.randint(0, 59)
        )).isoformat()

        # Generate chat history (1-3 exchanges)
        num_exchanges = random.randint(1, 3)
        chat_history = []

        for j in range(num_exchanges):
            # User message
            chat_history.append({
                "role": "user",
                "content": random.choice(user_messages)
            })
            # Assistant response
            chat_history.append({
                "role": "assistant",
                "content": random.choice(assistant_responses)
            })

        # Create document structure similar to MongoDB format
        doc = {
            "user_name": user_name,
            "conversation_id": conversation_id,
            "timestamp": timestamp,
            "request": {
                "message": random.choice(user_messages),
                "chat_history": chat_history
            }
        }

        sample_data.append(doc)

    return sample_data

def aggregate_chat_data_to_csv(
    num_records: int = 100,
    output_file: str = "chat_data.csv"
):
    """
    Generates sample chat data and saves it to CSV.

    Args:
        num_records: Number of chat records to generate (default: 100)
        output_file: Output CSV file path
    """

    # Generate sample data instead of connecting to MongoDB
    sample_documents = generate_sample_chat_data(num_records)

    # Prepare CSV data
    csv_data = []

    # Process each document
    for doc in sample_documents:
        user_name = doc.get("user_name", "")
        conversation_id = doc.get("conversation_id", "")
        timestamp = doc.get("timestamp", "")
        
        # Get latest message
        latest_message = doc.get("request", {}).get("message", "")
        
        # Process chat history
        chat_history = doc.get("request", {}).get("chat_history", [])
        
        for chat_item in chat_history:
            csv_data.append({
                "user_name": user_name,
                "conversation_id": conversation_id,
                "timestamp": timestamp,
                "message_type": "chat_history",
                "role": chat_item.get("role", ""),
                "content": chat_item.get("content", "")
            })
        
        # Add latest message
        if latest_message:
            csv_data.append({
                "user_name": user_name,
                "conversation_id": conversation_id,
                "timestamp": timestamp,
                "message_type": "latest_message",
                "role": "user",
                "content": latest_message
            })
    
    # Write to CSV
    if csv_data:
        fieldnames = ["user_name", "conversation_id", "timestamp", "message_type", "role", "content"]
        
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(csv_data)
        
        print(f"Successfully exported {len(csv_data)} records to {output_file}")
    else:
        print("No data found to export")

# Usage example
if __name__ == "__main__":
    aggregate_chat_data_to_csv(
        num_records=100,
        output_file="chat_aggregated_data.csv"
    )