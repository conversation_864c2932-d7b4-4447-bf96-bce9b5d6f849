from pymongo import MongoClient
from datetime import datetime

def get_monthly_request_counts():
    """
    Aggregates chat requests by month for requests after August 1st, 2024.
    Returns count of requests grouped by month.
    """

    # MongoDB aggregation pipeline
    pipeline = [
        # Filter: Only requests after August 1st, 2024
        {
            "$match": {
                "timestamp": {
                    "$lt": datetime(2024, 8, 1)
                }
            }
        },

        # Group by year and month, count requests
        {
            "$group": {
                "_id": {
                    "year": {"$year": "$timestamp"},
                    "month": {"$month": "$timestamp"}
                },
                "request_count": {"$sum": 1}
            }
        },

        # Sort by year and month
        {
            "$sort": {
                "_id.year": 1,
                "_id.month": 1
            }
        },

        # Format output for readability
        {
            "$project": {
                "_id": 0,
                "year": "$_id.year",
                "month": "$_id.month",
                "request_count": 1
            }
        }
    ]

    return pipeline
def execute_aggregation():
    """
    Execute the aggregation pipeline and print results.
    """
    try:
        # Connect to MongoDB (adjust connection string as needed)
        client = MongoClient('mongodb://************:8300/?directConnection=true&serverSelectionTimeoutMS=2000&appName=mongosh+2.2.10')
        db = client['AG_Classify_Bot_PROD']  # Replace with your database name
        collection = db['Bot_Responses']  # Replace with your collection name

        # Get the aggregation pipeline
        pipeline = get_monthly_request_counts()

        # Execute aggregation
        results = list(collection.aggregate(pipeline))

        # Print results
        print("Monthly Request Counts (after August 1st, 2024):")
        print("-" * 50)

        if results:
            for result in results:
                month_name = datetime(result['year'], result['month'], 1).strftime('%B')
                print(f"{month_name} {result['year']}: {result['request_count']} requests")
        else:
            print("No requests found after August 1st, 2024")

        return results

    except Exception as e:
        print(f"Error executing aggregation: {e}")
        return None
    finally:
        if 'client' in locals():
            client.close()

# Usage example
if __name__ == "__main__":
    execute_aggregation()