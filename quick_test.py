#!/usr/bin/env python3
"""
Quick test script to demonstrate the pricing calculation.
This script shows how to use the calculate_pricing_from_mongo function.
"""

from calculate_total_pricing import calculate_pricing_from_mongo
import json

def main():
    """Quick test of the pricing calculation."""
    
    print("🧪 Quick Test of Pricing Calculation")
    print("=" * 50)
    
    # Example usage - you'll need to replace these with your actual values
    print("📝 To use this script, you need to know:")
    print("   1. Your MongoDB database name")
    print("   2. Your MongoDB collection name")
    print("   3. Optionally, a query filter")
    print()
    
    # Example configurations you might use:
    examples = [
        {
            "description": "All documents in a collection",
            "database": "your_database_name",
            "collection": "your_collection_name",
            "query": {}
        },
        {
            "description": "Documents with specific status",
            "database": "your_database_name", 
            "collection": "your_collection_name",
            "query": {"status": "processed"}
        },
        {
            "description": "Documents from last 30 days",
            "database": "your_database_name",
            "collection": "your_collection_name", 
            "query": {
                "created_at": {
                    "$gte": "2024-01-01T00:00:00Z"  # Replace with actual date
                }
            }
        }
    ]
    
    print("📋 Example configurations:")
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['description']}:")
        print(f"   Database: {example['database']}")
        print(f"   Collection: {example['collection']}")
        print(f"   Query: {json.dumps(example['query'], indent=6)}")
    
    print("\n" + "=" * 50)
    print("💡 To run the actual calculation:")
    print("   1. Edit this script with your actual database/collection names")
    print("   2. Or run: python calculate_total_pricing.py")
    print("   3. Or use the function programmatically:")
    print()
    print("   from calculate_total_pricing import calculate_pricing_from_mongo")
    print("   total = calculate_pricing_from_mongo('db_name', 'collection_name')")
    print("   print(f'Total: {total:.6f} NPR')")
    
    # Uncomment and modify the following lines to run an actual test:
    # Replace 'your_database_name' and 'your_collection_name' with actual values
    
    # print("\n🚀 Running actual calculation...")
    # try:
    #     total_price = calculate_pricing_from_mongo(
    #         database_name="your_database_name",  # Replace this
    #         collection_name="your_collection_name",  # Replace this
    #         query={}  # Optional: add a filter query
    #     )
    #     print(f"\n💰 TOTAL PRICING: {total_price:.6f} NPR")
    # except Exception as e:
    #     print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
