#!/usr/bin/env python3
"""
<PERSON>ript to calculate total pricing from JSON files stored in MinIO bucket.
Fetches object names from MongoDB and retrieves JSON files from MinIO to sum total_price_nep values.
"""

import json
import os
import csv
from typing import List, Dict, Any, <PERSON><PERSON>
from datetime import datetime, timed<PERSON>ta
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
from dotenv import load_dotenv
from pymongo import MongoClient
from minio import Minio
import io

# Load environment variables
load_dotenv()

# MinIO configuration - try different configurations
MINIO_CONFIGS = [
    {
        "access_key": "minio-admin",
        "secret_key": "aLongPassword123",
        "bucket_name": "aroma.agsociar",
        "minio_url": "minio.nextai.asia",
        "secure": True
    }
]

# Default configuration (will be updated if connection test succeeds)
MINIO_CONFIG = MINIO_CONFIGS[0]

# Batch processing configuration
BATCH_CONFIG = {
    "batch_size": 50,      # Number of files to process in each batch
    "max_workers": 10,     # Maximum number of concurrent threads
    "show_progress": True  # Whether to show detailed progress
}

def connect_to_mongo() -> MongoClient:
    """Connect to MongoDB using the URI from environment variables."""
    mongo_uri = os.getenv('MONGO_URI')
    if not mongo_uri:
        raise ValueError("MONGO_URI not found in environment variables")
    
    client = MongoClient(mongo_uri)
    return client

def connect_to_minio() -> Minio:
    """Connect to MinIO using the provided configuration, trying different configs if needed."""
    global MINIO_CONFIG

    for i, config in enumerate(MINIO_CONFIGS):
        try:
            print(f"Trying MinIO connection #{i+1} to: {config['minio_url']}")
            client = Minio(
                # config["minio_url"],
                access_key=config["access_key"],
                secret_key=config["secret_key"],
                endpoint=config["minio_url"],
                secure=config["secure"]
            )

            # Test the connection by listing buckets
            buckets = list(client.list_buckets())
            print(f"✅ MinIO connection successful to: {config['minio_url']}")
            print(f"📁 Available buckets: {[b.name for b in buckets]}")

            # Check if target bucket exists
            bucket_names = [b.name for b in buckets]
            if config["bucket_name"] not in bucket_names:
                print(f"⚠️  Target bucket '{config['bucket_name']}' not found in available buckets")
                continue

            # Test bucket access by trying to list objects
            try:
                objects = list(client.list_objects(config["bucket_name"], recursive=True))
                print(f"✅ Bucket access confirmed. Found {len(objects)} objects")

                # Update the global config to the working one
                MINIO_CONFIG = config
                return client

            except Exception as bucket_error:
                print(f"❌ Bucket access failed: {bucket_error}")
                continue

        except Exception as e:
            print(f"❌ MinIO connection #{i+1} failed for {config['minio_url']}: {e}")
            continue

    raise Exception("All MinIO configurations failed")

def configure_batch_processing(batch_size: int = None, max_workers: int = None, show_progress: bool = None):
    """
    Configure batch processing settings.

    Args:
        batch_size: Number of files to process in each batch
        max_workers: Maximum number of concurrent threads
        show_progress: Whether to show detailed progress
    """
    global BATCH_CONFIG

    if batch_size is not None:
        BATCH_CONFIG["batch_size"] = batch_size
    if max_workers is not None:
        BATCH_CONFIG["max_workers"] = max_workers
    if show_progress is not None:
        BATCH_CONFIG["show_progress"] = show_progress

    print(f"📦 Batch processing configured:")
    print(f"   Batch size: {BATCH_CONFIG['batch_size']}")
    print(f"   Max workers: {BATCH_CONFIG['max_workers']}")
    print(f"   Show progress: {BATCH_CONFIG['show_progress']}")

def get_object_names_from_mongo(client: MongoClient, database_name: str, collection_name: str, query: Dict[str, Any] = None) -> List[Tuple[str, datetime]]:
    """
    Fetch object names and creation dates from MongoDB.

    Args:
        client: MongoDB client
        database_name: Name of the database
        collection_name: Name of the collection
        query: MongoDB query to filter documents (optional)

    Returns:
        List of tuples containing (object_name, created_at)
    """
    db = client[database_name]
    collection = db[collection_name]

    # Default query if none provided
    if query is None:
        query = {}

    # Fetch documents and extract object names with creation dates
    documents = collection.find(query).sort("created_at", -1)#.limit(1000)
    object_data = []

    for doc in documents:
        object_name = None
        created_at = None

        # Extract object name
        if 'object_name' in doc:
            object_name = doc['object_name']
        elif 'filename' in doc:
            object_name = doc['filename']
        elif 'file_path' in doc:
            object_name = doc['file_path']

        # Extract creation date
        if 'created_at' in doc:
            created_at = doc['created_at']
        elif 'createdAt' in doc:
            created_at = doc['createdAt']
        elif 'timestamp' in doc:
            created_at = doc['timestamp']
        elif '_id' in doc:
            # Extract timestamp from ObjectId if no explicit date field
            created_at = doc['_id'].generation_time

        if object_name and created_at:
            # Ensure created_at is a datetime object
            if isinstance(created_at, str):
                try:
                    created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                except ValueError:
                    try:
                        created_at = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        print(f"Warning: Could not parse date {created_at} for {object_name}")
                        continue

            object_data.append((object_name, created_at))
        else:
            print(f"Warning: Could not find object name or date in document: {doc.get('_id', 'unknown')}")

    return object_data

def group_objects_by_week(object_data: List[Tuple[str, datetime]]) -> Dict[str, List[str]]:
    """
    Group object names by week.

    Args:
        object_data: List of tuples containing (object_name, created_at)

    Returns:
        Dictionary with week strings as keys and lists of object names as values
    """
    weekly_groups = defaultdict(list)

    for object_name, created_at in object_data:
        # Get the start of the week (Monday)
        week_start = created_at - timedelta(days=created_at.weekday())
        week_start = week_start.replace(hour=0, minute=0, second=0, microsecond=0)

        # Create a week identifier string
        week_end = week_start + timedelta(days=6)
        week_key = f"{week_start.strftime('%Y-%m-%d')} to {week_end.strftime('%Y-%m-%d')}"

        weekly_groups[week_key].append(object_name)

    return dict(weekly_groups)

def export_weekly_pricing_to_csv(weekly_data: Dict[str, Dict[str, Any]], filename: str = "weekly_pricing.csv"):
    """
    Export weekly pricing and job data to CSV file.

    Args:
        weekly_data: Dictionary with week strings as keys and data dictionaries as values
        filename: Output CSV filename
    """
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)

            # Write header
            writer.writerow([
                'Week_Start', 'Week_End', 'Week_Range',
                'Price_NPR', 'Jobs_Count', 'Total_Objects', 'Processed_Objects',
                'Avg_Price_Per_Job', 'Percentage_of_Total_Price', 'Percentage_of_Total_Jobs'
            ])

            # Calculate totals for percentage calculation
            total_price = sum(data['price'] for data in weekly_data.values())
            total_jobs = sum(data['jobs'] for data in weekly_data.values())

            # Sort weeks chronologically
            sorted_weeks = sorted(weekly_data.items())

            for week_range, data in sorted_weeks:
                # Extract start and end dates from week range
                week_start, week_end = week_range.split(' to ')
                price_percentage = (data['price'] / total_price) * 100 if total_price > 0 else 0
                jobs_percentage = (data['jobs'] / total_jobs) * 100 if total_jobs > 0 else 0

                writer.writerow([
                    week_start,
                    week_end,
                    week_range,
                    f"{data['price']:.6f}",
                    data['jobs'],
                    data['total_objects'],
                    data['processed_objects'],
                    f"{data['avg_price_per_job']:.6f}",
                    f"{price_percentage:.2f}",
                    f"{jobs_percentage:.2f}"
                ])

            # Write total row
            avg_price_per_job_total = total_price / total_jobs if total_jobs > 0 else 0
            writer.writerow([
                'TOTAL',
                '',
                'ALL_WEEKS',
                f"{total_price:.6f}",
                total_jobs,
                sum(data['total_objects'] for data in weekly_data.values()),
                sum(data['processed_objects'] for data in weekly_data.values()),
                f"{avg_price_per_job_total:.6f}",
                '100.00',
                '100.00'
            ])

        print(f"✅ Weekly pricing and job data exported to: {filename}")

    except Exception as e:
        print(f"❌ Error exporting to CSV: {e}")

def fetch_json_from_minio(minio_client: Minio, bucket_name: str, object_name: str) -> Dict[str, Any]:
    """
    Fetch and parse JSON file from MinIO.

    Args:
        minio_client: MinIO client
        bucket_name: Name of the bucket
        object_name: Name of the object/file

    Returns:
        Parsed JSON data as dictionary
    """
    response = None
    try:
        # Check if object exists first
        try:
            minio_client.stat_object(bucket_name, object_name)
        except Exception as stat_error:
            print(f"❌ Object {object_name} not found or not accessible: {stat_error}")
            return {}

        response = minio_client.get_object(bucket_name, object_name)
        content = response.read().decode('utf-8')
        json_data = json.loads(content)
        # Verify it has the expected structure
        if 'pricing' not in json_data:
            print(f"⚠️  Warning: {object_name} does not contain 'pricing' field")
        elif 'total_price_nep' not in json_data.get('pricing', {}):
            print(f"⚠️  Warning: {object_name} does not contain 'pricing.total_price_nep' field")

        return json_data

    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing error for {object_name}: {e}")
        return {}
    except Exception as e:
        print(f"❌ Error fetching {object_name}: {e}")
        return {}
    finally:
        if response:
            response.close()
            response.release_conn()

def fetch_json_batch_from_minio(minio_client: Minio, bucket_name: str, object_names: List[str],
                               batch_size: int = None, max_workers: int = None, show_progress: bool = None) -> List[Dict[str, Any]]:
    """
    Fetch multiple JSON files from MinIO in parallel batches.

    Args:
        minio_client: MinIO client
        bucket_name: Name of the bucket
        object_names: List of object names to fetch
        batch_size: Number of objects to process in each batch (uses config default if None)
        max_workers: Maximum number of concurrent threads (uses config default if None)
        show_progress: Whether to show detailed progress (uses config default if None)

    Returns:
        List of parsed JSON data dictionaries
    """
    # Use configuration defaults if not specified
    if batch_size is None:
        batch_size = BATCH_CONFIG["batch_size"]
    if max_workers is None:
        max_workers = BATCH_CONFIG["max_workers"]
    if show_progress is None:
        show_progress = BATCH_CONFIG["show_progress"]

    json_files_data = []
    total_objects = len(object_names)
    processed_count = 0
    successful_count = 0

    print(f"🚀 Starting batch processing of {total_objects} objects...")
    print(f"📦 Batch size: {batch_size}, Max workers: {max_workers}")

    if total_objects == 0:
        print("⚠️  No objects to process")
        return []

    start_time = time.time()

    # Process in batches
    for batch_start in range(0, total_objects, batch_size):
        batch_end = min(batch_start + batch_size, total_objects)
        batch_objects = object_names[batch_start:batch_end]
        batch_num = (batch_start // batch_size) + 1
        total_batches = (total_objects + batch_size - 1) // batch_size

        print(f"\n📦 Processing batch {batch_num}/{total_batches} ({len(batch_objects)} objects)")
        batch_start_time = time.time()

        # Process batch in parallel
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks for this batch
            future_to_object = {
                executor.submit(fetch_json_from_minio, minio_client, bucket_name, obj_name): obj_name
                for obj_name in batch_objects
            }

            # Collect results as they complete
            batch_successful = 0
            for future in as_completed(future_to_object):
                object_name = future_to_object[future]
                try:
                    json_data = future.result()
                    processed_count += 1

                    if json_data:  # Only add non-empty results
                        json_files_data.append(json_data)
                        successful_count += 1
                        batch_successful += 1

                    # Show progress every 10 objects (if enabled)
                    if show_progress and processed_count % 10 == 0:
                        print(f"   ✅ Processed {processed_count}/{total_objects} objects...")

                except Exception as e:
                    print(f"   ❌ Error processing {object_name}: {e}")
                    processed_count += 1

        batch_time = time.time() - batch_start_time
        print(f"   📊 Batch {batch_num} completed: {batch_successful}/{len(batch_objects)} successful in {batch_time:.2f}s")

    total_time = time.time() - start_time
    print(f"\n🎉 Batch processing completed!")
    print(f"📊 Results: {successful_count}/{total_objects} objects successfully processed")
    print(f"⏱️  Total time: {total_time:.2f}s (avg: {total_time/total_objects:.3f}s per object)")
    print(f"🚀 Speed improvement: ~{max_workers}x faster than sequential processing")

    return json_files_data

def calculate_total_pricing(json_files_data: List[Dict[str, Any]]) -> Tuple[float, int]:
    """
    Calculate total pricing from list of JSON data.

    Args:
        json_files_data: List of parsed JSON data

    Returns:
        Tuple of (total_price, job_count)
    """
    total_price = 0.0
    processed_files = 0

    for data in json_files_data:
        try:
            if 'pricing' in data and 'total_price_nep' in data['pricing']:
                price = float(data['pricing']["total_price_nep"])
                total_price += price
                processed_files += 1
                print(f"Added price: {price:.6f} NPR")
            else:
                print("Warning: pricing.total_price_nep not found in JSON data")
        except (ValueError, TypeError) as e:
            print(f"Error processing pricing data: {e}")

    print(f"\nProcessed {processed_files} files successfully")
    return total_price, processed_files

def calculate_pricing_by_week_from_mongo(database_name: str, collection_name: str, query: Dict[str, Any] = None) -> Dict[str, Dict[str, Any]]:
    """
    Calculate total pricing and job count by week by fetching object names from MongoDB and JSON files from MinIO.

    Args:
        database_name: Name of the MongoDB database
        collection_name: Name of the MongoDB collection
        query: MongoDB query to filter documents (optional)

    Returns:
        Dictionary with week strings as keys and dictionaries containing 'price', 'jobs', 'avg_price_per_job' as values
    """
    mongo_client = None
    try:
        # Connect to MongoDB
        print("Connecting to MongoDB...")
        mongo_client = connect_to_mongo()

        # Connect to MinIO
        print("Connecting to MinIO...")
        minio_client = connect_to_minio()

        # Get object names with dates from MongoDB
        print(f"Fetching object names and dates from MongoDB...")
        object_data = get_object_names_from_mongo(mongo_client, database_name, collection_name, query)

        if not object_data:
            print("No object data found in MongoDB")
            return {}

        print(f"Found {len(object_data)} objects")

        # Group objects by week
        print("Grouping objects by week...")
        weekly_groups = group_objects_by_week(object_data)

        print(f"Found {len(weekly_groups)} weeks:")
        for week, objects in weekly_groups.items():
            print(f"  {week}: {len(objects)} objects")

        # Calculate pricing for each week
        weekly_data = {}

        for week, object_names in weekly_groups.items():
            print(f"\n📅 Processing week: {week}")
            print(f"Fetching {len(object_names)} JSON files from MinIO using batch processing...")

            # Use batch processing for faster fetching
            json_files_data = fetch_json_batch_from_minio(
                minio_client,
                MINIO_CONFIG["bucket_name"],
                object_names
            )

            # Calculate total pricing and job count for this week
            week_total, job_count = calculate_total_pricing(json_files_data)
            avg_price_per_job = week_total / job_count if job_count > 0 else 0.0

            weekly_data[week] = {
                'price': week_total,
                'jobs': job_count,
                'total_objects': len(object_names),  # Total objects found in MongoDB
                'processed_objects': len(json_files_data),  # Objects successfully fetched from MinIO
                'avg_price_per_job': avg_price_per_job
            }

            print(f"💰 Week total: {week_total:.6f} NPR")
            print(f"📊 Jobs processed: {job_count}")
            print(f"📈 Average per job: {avg_price_per_job:.6f} NPR")

        return weekly_data

    except Exception as e:
        print(f"Error: {e}")
        return {}
    finally:
        if mongo_client:
            mongo_client.close()

def calculate_pricing_from_mongo(database_name: str, collection_name: str, query: Dict[str, Any] = None) -> float:
    """
    Calculate total pricing by fetching object names from MongoDB and JSON files from MinIO.

    Args:
        database_name: Name of the MongoDB database
        collection_name: Name of the MongoDB collection
        query: MongoDB query to filter documents (optional)

    Returns:
        Total sum of all total_price_nep values
    """
    mongo_client = None
    try:
        # Connect to MongoDB
        print("Connecting to MongoDB...")
        mongo_client = connect_to_mongo()

        # Connect to MinIO
        print("Connecting to MinIO...")
        minio_client = connect_to_minio()

        # Get object names from MongoDB
        print(f"Fetching object names from MongoDB...")
        object_data = get_object_names_from_mongo(mongo_client, database_name, collection_name, query)

        if not object_data:
            print("No object data found in MongoDB")
            return 0.0

        # Extract just the object names for simple total calculation
        object_names = [obj_name for obj_name, _ in object_data]
        print(f"Found {len(object_names)} object names")

        # Fetch JSON files from MinIO using batch processing
        print("Fetching JSON files from MinIO using batch processing...")
        json_files_data = fetch_json_batch_from_minio(
            minio_client,
            MINIO_CONFIG["bucket_name"],
            object_names
        )

        # Calculate total pricing
        print("\nCalculating total pricing...")
        total_price, job_count = calculate_total_pricing(json_files_data)
        print(f"📊 Total jobs processed: {job_count}")

        return total_price

    except Exception as e:
        print(f"Error: {e}")
        return 0.0
    finally:
        if mongo_client:
            mongo_client.close()

def main():
    """Main function to orchestrate the pricing calculation."""
    try:
        # Get user input for database and collection
        database_name = "agsociar_aroma2_db"
        collection_name = "media"

        # Optional: Add a query filter
        # use_filter = input("Do you want to add a query filter? (y/n): ").strip().lower()
        query = {"metadata.type":"output", "metadata.process_type":"audio-transcribe-analysis"}
        # if use_filter == 'y':
        #     print("Enter MongoDB query as JSON (e.g., {'status': 'processed'}):")
        #     query_str = input().strip()
        #     if query_str:
        #         try:
        #             query = json.loads(query_str)
        #         except json.JSONDecodeError:
        #             print("Invalid JSON query, using empty query")
        #             query = {}

        # Calculate pricing by week
        print("\n🗓️  CALCULATING PRICING BY WEEK")
        print("="*60)
        weekly_data = calculate_pricing_by_week_from_mongo(database_name, collection_name, query)

        if weekly_data:
            print(f"\n📊 WEEKLY PRICING & JOB BREAKDOWN")
            print("="*80)

            total_all_weeks = 0.0
            total_all_jobs = 0
            sorted_weeks = sorted(weekly_data.items())

            for week, data in sorted_weeks:
                print(f"📅 {week}")
                print(f"   💰 Price: {data['price']:.6f} NPR")
                print(f"   📊 Jobs: {data['jobs']} jobs")
                print(f"   📈 Avg per job: {data['avg_price_per_job']:.6f} NPR")
                print(f"   📄 Objects: {data['processed_objects']}/{data['total_objects']} processed")
                print()
                total_all_weeks += data['price']
                total_all_jobs += data['jobs']

            avg_price_per_week = total_all_weeks / len(weekly_data) if len(weekly_data) > 0 else 0
            avg_jobs_per_week = total_all_jobs / len(weekly_data) if len(weekly_data) > 0 else 0
            avg_price_per_job_overall = total_all_weeks / total_all_jobs if total_all_jobs > 0 else 0

            print(f"{'='*80}")
            print(f"💰 TOTAL ACROSS ALL WEEKS: {total_all_weeks:.6f} NPR")
            print(f"� TOTAL JOBS ACROSS ALL WEEKS: {total_all_jobs} jobs")
            print(f"�📈 AVERAGE PRICE PER WEEK: {avg_price_per_week:.6f} NPR")
            print(f"📈 AVERAGE JOBS PER WEEK: {avg_jobs_per_week:.1f} jobs")
            print(f"💵 AVERAGE PRICE PER JOB: {avg_price_per_job_overall:.6f} NPR")
            print(f"🗓️  NUMBER OF WEEKS: {len(weekly_data)}")
            print(f"{'='*80}")

            # Export to CSV
            print(f"\n📄 EXPORTING DATA TO CSV")
            print("-"*60)
            export_weekly_pricing_to_csv(weekly_data, "weekly_pricing_report.csv")

        else:
            print("❌ No pricing data found")

        # Also calculate overall total for comparison
        print(f"\n🔄 CALCULATING OVERALL TOTAL (for comparison)")
        print("-"*60)
        total_price = calculate_pricing_from_mongo(database_name, collection_name, query)
        print(f"📊 Overall total: {total_price:.6f} NPR")

    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
