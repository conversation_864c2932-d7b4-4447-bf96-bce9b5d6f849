#!/usr/bin/env python3
"""
Test script for weekly pricing calculation functionality.
"""

from calculate_total_pricing import calculate_pricing_by_week_from_mongo, calculate_pricing_from_mongo

def main():
    """Test the weekly pricing calculation."""
    
    print("🧪 Testing Weekly Pricing Calculation")
    print("="*60)
    
    # Configuration
    database_name = "agsociar_aroma2_db"
    collection_name = "media"
    query = {"metadata.type": "output", "metadata.process_type": "generic-entity-extraction"}
    
    print(f"📋 Configuration:")
    print(f"   Database: {database_name}")
    print(f"   Collection: {collection_name}")
    print(f"   Query: {query}")
    print()
    
    try:
        # Test weekly calculation
        print("🗓️  CALCULATING PRICING BY WEEK")
        print("-"*60)
        weekly_data = calculate_pricing_by_week_from_mongo(database_name, collection_name, query)

        if weekly_data:
            print(f"\n📊 RESULTS:")
            print("="*80)

            total_all_weeks = 0.0
            total_all_jobs = 0
            sorted_weeks = sorted(weekly_data.items())

            for week, data in sorted_weeks:
                print(f"📅 {week}")
                print(f"   💰 Price: {data['price']:.6f} NPR")
                print(f"   📊 Jobs: {data['jobs']} jobs")
                print(f"   📈 Avg per job: {data['avg_price_per_job']:.6f} NPR")
                print(f"   📄 Objects: {data['processed_objects']}/{data['total_objects']} processed")
                total_all_weeks += data['price']
                total_all_jobs += data['jobs']
                print()

            avg_price_per_week = total_all_weeks / len(weekly_data) if len(weekly_data) > 0 else 0
            avg_jobs_per_week = total_all_jobs / len(weekly_data) if len(weekly_data) > 0 else 0
            avg_price_per_job_overall = total_all_weeks / total_all_jobs if total_all_jobs > 0 else 0

            print(f"{'='*80}")
            print(f"💰 TOTAL ACROSS ALL WEEKS: {total_all_weeks:.6f} NPR")
            print(f"📊 TOTAL JOBS ACROSS ALL WEEKS: {total_all_jobs} jobs")
            print(f"📈 AVERAGE PRICE PER WEEK: {avg_price_per_week:.6f} NPR")
            print(f"📈 AVERAGE JOBS PER WEEK: {avg_jobs_per_week:.1f} jobs")
            print(f"💵 AVERAGE PRICE PER JOB: {avg_price_per_job_overall:.6f} NPR")
            print(f"🗓️  NUMBER OF WEEKS: {len(weekly_data)}")
            print(f"{'='*80}")

            # Show weekly breakdown as a detailed table
            print(f"\n📋 WEEKLY SUMMARY TABLE:")
            print("-"*100)
            print(f"{'Week':<25} {'Price (NPR)':<15} {'Jobs':<8} {'Avg/Job':<12} {'% Price':<10} {'% Jobs':<8}")
            print("-"*100)

            for week, data in sorted_weeks:
                price_percentage = (data['price'] / total_all_weeks) * 100 if total_all_weeks > 0 else 0
                jobs_percentage = (data['jobs'] / total_all_jobs) * 100 if total_all_jobs > 0 else 0
                print(f"{week:<25} {data['price']:<15.6f} {data['jobs']:<8} {data['avg_price_per_job']:<12.6f} {price_percentage:<10.2f}% {jobs_percentage:<8.2f}%")

            print("-"*100)
            print(f"{'TOTAL':<25} {total_all_weeks:<15.6f} {total_all_jobs:<8} {avg_price_per_job_overall:<12.6f} {'100.00%':<10} {'100.00%':<8}")
            print("-"*100)
            
        else:
            print("❌ No weekly pricing data found")
            
    except Exception as e:
        print(f"❌ Error during weekly calculation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
