#!/usr/bin/env python3
"""
Example usage of the pricing calculation script.
This shows how to use the calculate_pricing_from_mongo function programmatically.
"""

from calculate_total_pricing import calculate_pricing_from_mongo

def main():
    """Example usage of the pricing calculation."""
    
    # Example 1: Calculate pricing for all documents in a collection
    print("Example 1: All documents")
    database_name = "your_database_name"  # Replace with your actual database name
    collection_name = "your_collection_name"  # Replace with your actual collection name
    
    total_price = calculate_pricing_from_mongo(database_name, collection_name)
    print(f"Total pricing for all documents: {total_price:.6f} NPR\n")
    
    # Example 2: Calculate pricing with a filter query
    print("Example 2: With filter query")
    query = {"status": "processed"}  # Example filter - adjust based on your data
    
    total_price_filtered = calculate_pricing_from_mongo(database_name, collection_name, query)
    print(f"Total pricing for filtered documents: {total_price_filtered:.6f} NPR\n")
    
    # Example 3: Calculate pricing for documents within a date range
    print("Example 3: Date range filter")
    from datetime import datetime, timedelta
    
    # Example: last 30 days (adjust field name and date format based on your schema)
    thirty_days_ago = datetime.now() - timedelta(days=30)
    date_query = {
        "created_at": {
            "$gte": thirty_days_ago
        }
    }
    
    total_price_recent = calculate_pricing_from_mongo(database_name, collection_name, date_query)
    print(f"Total pricing for last 30 days: {total_price_recent:.6f} NPR\n")

if __name__ == "__main__":
    main()
