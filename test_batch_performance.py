#!/usr/bin/env python3
"""
Test script to demonstrate batch processing performance improvements.
"""

from calculate_total_pricing import (
    calculate_pricing_by_week_from_mongo, 
    configure_batch_processing,
    BATCH_CONFIG
)
import time

def test_different_batch_sizes():
    """Test different batch configurations to find optimal settings."""
    
    print("🧪 Testing Batch Processing Performance")
    print("="*60)
    
    # Configuration
    database_name = "agsociar_aroma2_db"
    collection_name = "media"
    query = {"metadata.type": "output", "metadata.process_type": "generic-entity-extraction"}
    
    # Different configurations to test
    test_configs = [
        {"batch_size": 25, "max_workers": 5, "name": "Small batches, few workers"},
        {"batch_size": 50, "max_workers": 10, "name": "Medium batches, medium workers"},
        {"batch_size": 100, "max_workers": 15, "name": "Large batches, many workers"},
        {"batch_size": 75, "max_workers": 12, "name": "Optimized setting"}
    ]
    
    results = []
    
    for i, config in enumerate(test_configs, 1):
        print(f"\n🔧 Test {i}/{len(test_configs)}: {config['name']}")
        print(f"   Batch size: {config['batch_size']}, Max workers: {config['max_workers']}")
        print("-" * 60)
        
        # Configure batch processing
        configure_batch_processing(
            batch_size=config['batch_size'],
            max_workers=config['max_workers'],
            show_progress=False  # Disable progress for cleaner output
        )
        
        # Measure time
        start_time = time.time()
        
        try:
            weekly_data = calculate_pricing_by_week_from_mongo(database_name, collection_name, query)
            
            end_time = time.time()
            duration = end_time - start_time
            
            if weekly_data:
                total_price = sum(data['price'] for data in weekly_data.values())
                total_jobs = sum(data['jobs'] for data in weekly_data.values())
                total_objects = sum(data['total_objects'] for data in weekly_data.values())
                
                results.append({
                    'config': config,
                    'duration': duration,
                    'total_price': total_price,
                    'total_jobs': total_jobs,
                    'total_objects': total_objects,
                    'objects_per_second': total_objects / duration if duration > 0 else 0
                })
                
                print(f"✅ Completed in {duration:.2f} seconds")
                print(f"📊 Processed {total_objects} objects ({total_objects/duration:.1f} objects/sec)")
                print(f"💰 Total: {total_price:.6f} NPR, Jobs: {total_jobs}")
                
            else:
                print("❌ No data returned")
                
        except Exception as e:
            print(f"❌ Error: {e}")
            end_time = time.time()
            duration = end_time - start_time
            results.append({
                'config': config,
                'duration': duration,
                'error': str(e)
            })
    
    # Show comparison
    print(f"\n📊 PERFORMANCE COMPARISON")
    print("="*80)
    print(f"{'Configuration':<25} {'Time (s)':<10} {'Objects/sec':<12} {'Total Objects':<15}")
    print("-"*80)
    
    best_performance = None
    best_speed = 0
    
    for result in results:
        if 'error' not in result:
            config_name = result['config']['name'][:24]
            duration = result['duration']
            speed = result['objects_per_second']
            total_objects = result['total_objects']
            
            print(f"{config_name:<25} {duration:<10.2f} {speed:<12.1f} {total_objects:<15}")
            
            if speed > best_speed:
                best_speed = speed
                best_performance = result
        else:
            config_name = result['config']['name'][:24]
            print(f"{config_name:<25} {'ERROR':<10} {'-':<12} {'-':<15}")
    
    print("-"*80)
    
    if best_performance:
        print(f"\n🏆 BEST PERFORMANCE:")
        print(f"   Configuration: {best_performance['config']['name']}")
        print(f"   Batch size: {best_performance['config']['batch_size']}")
        print(f"   Max workers: {best_performance['config']['max_workers']}")
        print(f"   Speed: {best_performance['objects_per_second']:.1f} objects/second")
        print(f"   Total time: {best_performance['duration']:.2f} seconds")
        
        # Set as default
        configure_batch_processing(
            batch_size=best_performance['config']['batch_size'],
            max_workers=best_performance['config']['max_workers'],
            show_progress=True
        )
        print(f"\n✅ Applied best configuration as default")

def main():
    """Main function to run performance tests."""
    
    print("⚡ Batch Processing Performance Tester")
    print("="*60)
    print("This script will test different batch processing configurations")
    print("to find the optimal settings for your system and network.")
    print()
    
    choice = input("Do you want to run performance tests? (y/n): ").strip().lower()
    
    if choice == 'y':
        test_different_batch_sizes()
    else:
        print("📋 Current batch configuration:")
        print(f"   Batch size: {BATCH_CONFIG['batch_size']}")
        print(f"   Max workers: {BATCH_CONFIG['max_workers']}")
        print(f"   Show progress: {BATCH_CONFIG['show_progress']}")
        print()
        print("💡 You can modify these settings by calling:")
        print("   configure_batch_processing(batch_size=X, max_workers=Y)")

if __name__ == "__main__":
    main()
